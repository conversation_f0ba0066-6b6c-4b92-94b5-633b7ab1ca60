import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';

List<CameraDescription> cameras = [];

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  print('🚀 应用启动 - 开始初始化');
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '透卡相机',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const SimpleCameraScreen(),
    );
  }
}

class SimpleCameraScreen extends StatefulWidget {
  const SimpleCameraScreen({super.key});

  @override
  State<SimpleCameraScreen> createState() => _SimpleCameraScreenState();
}

class _SimpleCameraScreenState extends State<SimpleCameraScreen> {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isInitializing = false;
  String _statusMessage = '正在初始化...';

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    print('📱 开始初始化应用');

    setState(() {
      _statusMessage = '请求相机权限...';
    });

    // 请求相机权限
    final cameraStatus = await Permission.camera.request();
    print('📷 相机权限状态: $cameraStatus');

    if (cameraStatus.isGranted) {
      await _initializeCamera();
    } else {
      setState(() {
        _statusMessage = '相机权限被拒绝';
      });
    }
  }

  Future<void> _initializeCamera() async {
    if (_isInitializing) return;

    setState(() {
      _isInitializing = true;
      _statusMessage = '初始化相机...';
    });

    try {
      cameras = await availableCameras();
      print('📷 发现 ${cameras.length} 个相机设备');

      if (cameras.isEmpty) {
        setState(() {
          _statusMessage = '没有发现可用的相机设备';
        });
        return;
      }

      _controller = CameraController(
        cameras[0],
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _controller!.initialize();

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _statusMessage = '相机初始化成功';
        });
      }
    } catch (e) {
      print('❌ 相机初始化失败: $e');
      setState(() {
        _statusMessage = '相机初始化失败: $e';
      });
    } finally {
      _isInitializing = false;
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('🖼️ 构建界面 - 屏幕尺寸: ${MediaQuery.of(context).size}');

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('透卡相机测试', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.black,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 状态信息
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.blue.withOpacity(0.2),
              child: Text(
                _statusMessage,
                style: const TextStyle(color: Colors.white, fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),

            // 相机预览区域
            Expanded(
              child: Container(
                width: double.infinity,
                color: Colors.black,
                child: _buildCameraPreview(),
              ),
            ),

            // 底部按钮区域
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: _isInitialized ? null : _initializeCamera,
                    child: const Text('重新初始化'),
                  ),
                  ElevatedButton(
                    onPressed: _isInitialized ? _takePicture : null,
                    child: const Text('拍照'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCameraPreview() {
    if (!_isInitialized || _controller == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (_isInitializing)
              const CircularProgressIndicator(color: Colors.white),
            const SizedBox(height: 16),
            Text(
              _statusMessage,
              style: const TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Center(
      child: AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: CameraPreview(_controller!),
      ),
    );
  }

  Future<void> _takePicture() async {
    if (!_isInitialized || _controller == null) return;

    try {
      final image = await _controller!.takePicture();
      print('📸 拍照成功: ${image.path}');

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('拍照成功: ${image.path}')));
      }
    } catch (e) {
      print('❌ 拍照失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('拍照失败: $e')));
      }
    }
  }
}
