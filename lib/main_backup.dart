import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';
import 'dart:io';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:image_picker/image_picker.dart';
import 'gallery_page.dart';
import 'png_watermark_manager.dart';
import 'png_menu_page.dart';
import 'camera_lens_manager.dart';
import 'widgets/camera_controls_panel.dart';
import 'widgets/lens_selection_panel.dart';
import 'widgets/top_menu_panel.dart';

List<CameraDescription> cameras = [];

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 相机列表将在权限获取成功后获取
  print('🚀 应用启动 - 开始初始化');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '透卡相机',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const CameraScreen(),
    );
  }
}

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen>
    with WidgetsBindingObserver {
  CameraController? _controller;
  bool _isInitialized = false;
  bool _isTakingPicture = false;
  final ImagePicker _picker = ImagePicker();
  bool _isAppInBackground = false;
  final PngWatermarkManager _watermarkManager = PngWatermarkManager();
  final CameraLensManager _lensManager = CameraLensManager();
  bool _isLoadingMenu = false;
  bool _isDisposing = false; // 新增：标记是否正在释放资源

  // 初始化锁和状态跟踪变量
  bool _isInitializing = false; // 防止并发初始化
  bool _hasInitializedOnce = false; // 跟踪是否已经初始化过

  // 摄像头切换相关变量
  int _currentCameraIndex = 0; // 当前使用的摄像头索引（0=后置，1=前置）
  bool _isSwitchingCamera = false;

  // 相机控制状态
  double _currentZoom = 1.0; // 当前缩放级别
  double _minZoom = 1.0; // 最小缩放
  double _maxZoom = 1.0; // 最大缩放
  FlashMode _flashMode = FlashMode.auto; // 闪光灯模式
  double _exposureOffset = 0.0; // 曝光补偿
  double _minExposure = 0.0; // 最小曝光
  double _maxExposure = 0.0; // 最大曝光
  bool _showCameraControls = false; // 是否显示相机控制面板

  // 新UI状态变量
  bool _showTopMenu = false; // 是否显示右上角菜单
  bool _showLensSelection = false; // 是否显示镜头选择面板
  bool _isLensExpanded = false; // 顶部标题是否展开

  // 手势控制相关变量
  double _initialScale = 1.0;
  double _initialRotation = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _requestPermissions();
  }

  Future<void> _requestPermissions() async {
    print('📱 开始权限检查流程');

    try {
      // 获取设备信息
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        print(
          '📱 Android设备信息: API ${androidInfo.version.sdkInt}, 型号: ${androidInfo.model}',
        );
      }

      // 检查当前相机权限状态
      final currentCameraStatus = await Permission.camera.status;
      print('📷 当前相机权限状态: $currentCameraStatus');

      // 请求相机权限
      print('📷 正在请求相机权限...');
      final cameraStatus = await Permission.camera.request();
      print('📷 相机权限请求结果: $cameraStatus');

      // 对于Android 10及以上，需要特殊处理存储权限
      bool storageGranted = false;
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        print('💾 开始处理存储权限 (Android API ${androidInfo.version.sdkInt})');

        if (androidInfo.version.sdkInt >= 30) {
          // Android 11及以上：优先检查基本存储权限，MANAGE_EXTERNAL_STORAGE为可选
          print('💾 Android 11+: 检查存储权限');

          // 首先检查基本的存储权限
          final writeStatus = await Permission.storage.status;
          print('💾 WRITE_EXTERNAL_STORAGE权限状态: $writeStatus');

          if (writeStatus.isGranted) {
            storageGranted = true;
            print('💾 基本存储权限已授予');
          } else {
            // 尝试请求基本存储权限
            final requestedWriteStatus = await Permission.storage.request();
            print('💾 请求WRITE_EXTERNAL_STORAGE权限结果: $requestedWriteStatus');

            if (requestedWriteStatus.isGranted) {
              storageGranted = true;
            } else {
              // 基本权限未授予，检查MANAGE_EXTERNAL_STORAGE
              final manageStorageStatus =
                  await Permission.manageExternalStorage.status;
              print('💾 MANAGE_EXTERNAL_STORAGE权限状态: $manageStorageStatus');

              if (manageStorageStatus.isGranted) {
                storageGranted = true;
                print('💾 MANAGE_EXTERNAL_STORAGE权限已授予');
              } else {
                // 即使没有完整的存储权限，也允许使用应用私有目录
                print('💾 存储权限未完全授予，将使用应用私有目录');
                storageGranted = true; // 允许继续使用
              }
            }
          }
        } else if (androidInfo.version.sdkInt >= 29) {
          // Android 10使用Scoped Storage，不需要存储权限
          print('💾 Android 10: 使用Scoped Storage，无需额外存储权限');
          storageGranted = true;
        } else {
          // Android 9及以下需要存储权限
          print('💾 Android 9及以下: 请求传统存储权限');
          final currentStorageStatus = await Permission.storage.status;
          print('💾 当前存储权限状态: $currentStorageStatus');

          final storageStatus = await Permission.storage.request();
          print('💾 存储权限请求结果: $storageStatus');
          storageGranted = storageStatus.isGranted;
        }
      } else {
        // iOS需要相册权限
        print('💾 iOS: 请求相册权限');
        final currentPhotosStatus = await Permission.photos.status;
        print('💾 当前相册权限状态: $currentPhotosStatus');

        final photosStatus = await Permission.photos.request();
        print('💾 相册权限请求结果: $photosStatus');
        storageGranted = photosStatus.isGranted;
      }

      // 权限检查结果汇总
      print('✅ 权限检查完成:');
      print('   📷 相机权限: ${cameraStatus.isGranted ? "已授权" : "未授权"}');
      print('   💾 存储权限: ${storageGranted ? "已授权" : "未授权"}');

      if (cameraStatus.isGranted) {
        print('🎉 相机权限已获得，开始初始化相机');

        // 确保在主线程中初始化相机
        if (mounted) {
          await _initializeCamera();
        }

        if (!storageGranted && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('存储权限未授予，照片将保存到应用私有目录'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      } else if (cameraStatus.isPermanentlyDenied) {
        // 相机权限被永久拒绝，必须引导用户去设置
        print('❌ 相机权限被永久拒绝，需要用户手动开启');
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: const Text('需要相机权限'),
              content: const Text('应用需要相机权限才能拍照，请在设置中开启相机权限'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  },
                  child: const Text('去设置'),
                ),
              ],
            ),
          );
        }
      } else {
        // 相机权限被临时拒绝，显示重试选项
        print('⚠️ 相机权限被拒绝，显示重试选项');
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) => AlertDialog(
              title: const Text('需要相机权限'),
              content: const Text('应用需要相机权限才能正常工作，请允许相机权限'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _requestPermissions(); // 重新请求权限
                  },
                  child: const Text('重试'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      print('❌ 权限请求过程中出错: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('权限请求失败: $e')));
      }
    }
  }

  Future<void> _initializeCamera() async {
    // 防止并发初始化
    if (_isInitializing) {
      print('⚠️ 相机正在初始化中，跳过重复初始化');
      return;
    }

    _isInitializing = true;

    try {
      print('📷 开始获取可用相机列表...');

      // 确保在获取相机列表前有足够的延迟
      await Future.delayed(const Duration(milliseconds: 500));

      cameras = await availableCameras();
      print('📷 发现 ${cameras.length} 个相机设备');

      for (int i = 0; i < cameras.length; i++) {
        print('   相机 $i: ${cameras[i].name} (${cameras[i].lensDirection})');
      }

      if (cameras.isEmpty) {
        print('❌ 没有发现可用的相机设备');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('没有发现可用的相机设备')));
        }
        _isInitializing = false;
        return;
      }

      // 初始化镜头管理器
      await _lensManager.initializeLenses();
    } catch (e) {
      print('❌ 获取相机列表失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('获取相机列表失败: $e')));
      }
      _isInitializing = false;
      return;
    }

    // 检查是否正在释放资源
    if (_isDisposing) {
      print('⚠️ 正在释放资源，取消初始化');
      _isInitializing = false;
      return;
    }

    print('📷 正在初始化相机控制器...');

    // 确保使用有效的相机索引
    if (_currentCameraIndex >= cameras.length) {
      _currentCameraIndex = 0;
    }

    _controller = CameraController(
      cameras[_currentCameraIndex], // 使用当前选择的摄像头
      ResolutionPreset.high, // 改为high分辨率，避免某些设备不支持max
      enableAudio: false, // 禁用音频
      imageFormatGroup: ImageFormatGroup.jpeg, // 使用JPEG格式获得更好的兼容性
    );

    try {
      print('📷 开始初始化相机控制器...');
      await _controller!.initialize();

      // 再次检查是否正在释放资源
      if (_isDisposing || !mounted) {
        print('⚠️ 初始化过程中检测到释放操作，清理资源');
        await _controller?.dispose();
        _controller = null;
        _isInitializing = false;
        return;
      }

      // 等待一小段时间确保相机完全准备好
      await Future.delayed(const Duration(milliseconds: 500));

      print('✅ 相机控制器初始化成功');

      // 获取相机能力参数
      await _initializeCameraCapabilities();

      if (mounted && !_isDisposing) {
        setState(() {
          _isInitialized = true;
        });
        print('🎉 相机界面已更新，_isInitialized = $_isInitialized');
        _hasInitializedOnce = true;
      }
    } catch (e) {
      print('❌ 相机控制器初始化错误: $e');

      // 清理失败的控制器
      if (_controller != null) {
        try {
          await _controller!.dispose();
        } catch (disposeError) {
          print('⚠️ 清理失败的控制器时出错: $disposeError');
        }
        _controller = null;
      }

      if (mounted && !_isDisposing) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('相机初始化失败: $e')));

        // 显示一个简单的错误界面而不是空白
        setState(() {
          _isInitialized = false;
        });
      }
    } finally {
      _isInitializing = false;
    }
  }

  Future<void> _takePicture() async {
    if (!_isCameraReady || _isTakingPicture) {
      print('⚠️ 相机未准备好或正在拍照中');
      return;
    }

    // 检查相机状态
    if (_controller!.value.hasError) {
      print('❌ 相机状态异常，尝试重新初始化...');
      await _reinitializeCamera();
      return;
    }

    setState(() {
      _isTakingPicture = true;
    });

    try {
      print('📸 开始拍照...');
      print(
        '📸 相机状态: 已初始化=${_controller!.value.isInitialized}, 预览暂停=${_controller!.value.isPreviewPaused}',
      );

      // 确保预览没有暂停
      if (_controller!.value.isPreviewPaused) {
        print('📷 预览已暂停，恢复预览...');
        await _controller!.resumePreview();
        await Future.delayed(const Duration(milliseconds: 200));
      }

      // 再次检查相机状态
      if (!_isCameraReady) {
        print('⚠️ 拍照前相机状态异常');
        return;
      }

      // 拍照
      final XFile image = await _controller!.takePicture();
      print('📸 拍照完成: ${image.path}');

      // 添加水印并保存
      final String? savedPath = await _addWatermarkAndSave(image.path);

      if (savedPath != null) {
        print('💾 照片保存成功: $savedPath');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片已保存到相册')));
        }
      } else {
        print('❌ 照片保存失败');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('照片保存失败')));
        }
      }

      // 确保相机预览恢复正常
      await _ensureCameraPreview();
    } catch (e) {
      print('❌ 拍照错误: $e');
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('拍照失败: $e')));
      }

      // 发生错误时也要确保相机预览恢复
      await _ensureCameraPreview();
    } finally {
      if (mounted) {
        setState(() {
          _isTakingPicture = false;
        });
      }
      print('📸 拍照流程完成');
    }
  }

  // 确保相机预览正常工作
  Future<void> _ensureCameraPreview() async {
    try {
      if (!_isCameraReady) {
        print('⚠️ 相机未准备好，无法恢复预览');
        return;
      }

      if (_controller != null &&
          _controller!.value.isInitialized &&
          !_isDisposing) {
        // 检查预览是否正常
        if (!_controller!.value.isPreviewPaused) {
          print('📷 相机预览正常');
          return;
        }

        print('📷 相机预览暂停，尝试恢复...');

        // 在某些设备上，拍照后预览可能会暂停，需要重新启动
        await _controller!.pausePreview();
        await Future.delayed(const Duration(milliseconds: 100));

        // 再次检查状态
        if (_isCameraReady) {
          await _controller!.resumePreview();
          print('📷 相机预览已恢复');
        }
      }
    } catch (e) {
      print('⚠️ 恢复相机预览时出错: $e');
      // 如果恢复失败，尝试重新初始化相机
      if (!_isDisposing && mounted) {
        await _reinitializeCamera();
      }
    }
  }

  // 重新初始化相机
  Future<void> _reinitializeCamera() async {
    // 防止重复初始化
    if (_isDisposing || _isInitializing) {
      print('⚠️ 正在释放资源或初始化中，取消重新初始化');
      return;
    }

    _isInitializing = true;

    try {
      print('🔄 重新初始化相机...');

      // 确保先清理现有资源
      if (_controller != null) {
        try {
          _isDisposing = true;
          await _controller!.dispose();
        } catch (e) {
          print('⚠️ 清理旧相机控制器时出错: $e');
        } finally {
          _controller = null;
          _isDisposing = false;
        }
      }

      if (!mounted) {
        print('⚠️ Widget已卸载，取消初始化');
        return;
      }

      setState(() {
        _isInitialized = false;
      });

      // 等待一小段时间确保资源完全释放
      await Future.delayed(const Duration(milliseconds: 300));

      if (cameras.isNotEmpty && mounted && !_isDisposing) {
        _controller = CameraController(
          cameras[_currentCameraIndex],
          ResolutionPreset.high, // 使用high分辨率
          enableAudio: false,
          imageFormatGroup: ImageFormatGroup.jpeg, // 使用JPEG格式
        );

        await _controller!.initialize();

        // 再次检查状态
        if (!mounted || _isDisposing) {
          print('⚠️ 初始化过程中状态改变，清理资源');
          await _controller?.dispose();
          _controller = null;
          return;
        }

        // 再等待一小段时间确保相机完全准备好
        await Future.delayed(const Duration(milliseconds: 200));

        if (mounted && !_isDisposing) {
          setState(() {
            _isInitialized = true;
          });
        }

        print('✅ 相机重新初始化成功');
        _hasInitializedOnce = true;
      }
    } catch (e) {
      print('❌ 重新初始化相机失败: $e');
      // 如果重新初始化失败，再次尝试
      if (mounted && !_isDisposing) {
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted && !_isInitialized && !_isDisposing && !_isInitializing) {
            print('🔄 重试相机初始化...');
            _reinitializeCamera();
          }
        });
      }
    } finally {
      _isInitializing = false;
    }
  }

  Future<String?> _addWatermarkAndSave(String imagePath) async {
    try {
      // 读取图片
      final bytes = await File(imagePath).readAsBytes();
      final img.Image? image = img.decodeImage(bytes);

      if (image == null) return null;

      // 添加水印
      final watermarkedImage = _addWatermark(image);

      // 获取真正的相册目录（DCIM/Camera）
      Directory? picturesDir;
      String savePath;

      if (Platform.isAndroid) {
        // Android: 在DCIM目录下创建专门的透卡相机文件夹
        final externalDir = await getExternalStorageDirectory();
        if (externalDir != null) {
          // 构建 DCIM/TransparentCard 路径（在相册中创建专门文件夹）
          final dcimPath = externalDir.path.replaceAll(
            '/Android/data/com.example.watermark_camera/files',
            '/DCIM/TransparentCard',
          );
          picturesDir = Directory(dcimPath);
          print('📁 保存目录: $dcimPath');
        }
      } else {
        // iOS: 在文档目录下创建透卡相机文件夹
        final documentsDir = await getApplicationDocumentsDirectory();
        picturesDir = Directory(
          path.join(documentsDir.path, 'TransparentCard'),
        );
      }

      if (picturesDir == null) {
        print('❌ 无法获取保存目录');
        return null;
      }

      // 确保目录存在
      if (!await picturesDir.exists()) {
        await picturesDir.create(recursive: true);
        print('📁 创建保存目录: ${picturesDir.path}');
      }

      // 生成文件名（使用更友好的格式）
      final now = DateTime.now();
      final formatter = DateFormat('yyyyMMdd_HHmmss');
      final fileName = 'TransparentCard_${formatter.format(now)}.jpg';
      savePath = path.join(picturesDir.path, fileName);

      print('💾 保存路径: $savePath');

      // 保存带水印的图片
      await File(savePath).writeAsBytes(img.encodeJpg(watermarkedImage));
      print('✅ 照片已保存到: $savePath');

      // 删除原始图片
      await File(imagePath).delete();

      return savePath;
    } catch (e) {
      print('添加水印错误: $e');
      return null;
    }
  }

  // 切换前后置摄像头
  Future<void> _switchCamera() async {
    if (_isSwitchingCamera || cameras.length < 2) return;

    setState(() {
      _isSwitchingCamera = true;
    });

    try {
      // 切换到下一个摄像头
      _currentCameraIndex = (_currentCameraIndex + 1) % cameras.length;

      print(
        '📷 切换到摄像头: ${cameras[_currentCameraIndex].name} (${cameras[_currentCameraIndex].lensDirection})',
      );

      // 重新初始化相机
      await _reinitializeCamera();
    } catch (e) {
      print('❌ 切换摄像头失败: $e');
      // 如果切换失败，回退到原来的摄像头
      _currentCameraIndex = (_currentCameraIndex - 1) % cameras.length;
    } finally {
      setState(() {
        _isSwitchingCamera = false;
      });
    }
  }

  // 切换到指定镜头
  Future<void> _switchToLens(int lensIndex) async {
    if (_isSwitchingCamera ||
        lensIndex >= _lensManager.availableLenses.length) {
      return;
    }

    setState(() {
      _isSwitchingCamera = true;
    });

    try {
      // 使用镜头管理器切换镜头
      final success = _lensManager.switchToLens(lensIndex);

      if (success) {
        // 更新当前相机索引
        final selectedLens = _lensManager.availableLenses[lensIndex];
        _currentCameraIndex = cameras.indexWhere(
          (camera) => camera == selectedLens.camera,
        );

        print('📷 切换到镜头: ${selectedLens.displayName}');

        // 重新初始化相机
        await _reinitializeCamera();
      } else {
        print('❌ 镜头切换失败：无效的镜头索引');
      }
    } catch (e) {
      print('❌ 切换镜头失败: $e');
    } finally {
      setState(() {
        _isSwitchingCamera = false;
      });
    }
  }

  // 循环切换闪光灯模式
  void _cycleFlashMode() {
    FlashMode nextMode;
    switch (_flashMode) {
      case FlashMode.auto:
        nextMode = FlashMode.off;
        break;
      case FlashMode.off:
        nextMode = FlashMode.always;
        break;
      case FlashMode.always:
        nextMode = FlashMode.auto;
        break;
      case FlashMode.torch:
        nextMode = FlashMode.auto;
        break;
    }

    _safeExecuteCameraOperation(
      () => _controller!.setFlashMode(nextMode),
      operationName: '切换闪光灯模式',
    ).then((_) {
      if (mounted) {
        setState(() {
          _flashMode = nextMode;
        });
      }
    });
  }

  img.Image _addWatermark(img.Image image) {
    // 如果选择了PNG水印，应用PNG水印
    final selectedWatermark = _watermarkManager.selectedWatermark;
    if (selectedWatermark != null) {
      // 获取当前屏幕尺寸作为预览尺寸参考
      final screenSize = MediaQuery.of(context).size;
      final previewRect = _watermarkManager.calculateCameraPreviewRect(
        screenSize,
        selectedWatermark,
      );

      return _watermarkManager.applyWatermarkToImage(
            image,
            selectedWatermark,
            previewSize: Size(previewRect.width, previewRect.height),
          ) ??
          image;
    }

    // 如果没有选择PNG水印，直接返回原图（不添加任何水印）
    return image;
  }

  // 构建相机预览组件（保持宽高比，支持点击聚焦）
  Widget _buildCameraPreview() {
    print(
      '🖼️ 构建相机预览 - _isInitialized: $_isInitialized, _isCameraReady: $_isCameraReady',
    );

    if (!_isInitialized || !_isCameraReady) {
      return Positioned.fill(
        child: Container(
          color: Colors.black,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isInitializing) ...[
                  const CircularProgressIndicator(color: Colors.white),
                  const SizedBox(height: 16),
                  const Text(
                
